const { createApp } = Vue;

const ServiceCard = {
    props: {
        service: {
            type: Object,
            required: true
        }
    },
    template: `
        <article class="service-card"
             :class="{ 'service-card--inverted': service.invert }"
             @click="handleClick"
             @keydown.enter="handleClick"
             @keydown.space.prevent="handleClick"
             tabindex="0"
             role="button"
             :aria-label="service.title + ' Service öffnen: ' + service.description"
             :aria-describedby="'service-desc-' + service.id">
            <header class="service-header">
                <div :class="service.id === 8 ? 'service-icon--pink' : 'service-icon'" aria-hidden="true">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path v-for="path in service.iconPaths" :key="path" :d="path"/>
                    </svg>
                </div>
                <h5 class="service-title">{{ service.title }}<span v-if="service.priority" class="badge">PRIORITÄT</span></h5>
            </header>
            <p class="service-description" :id="'service-desc-' + service.id">{{ service.description }}</p>
            <div class="service-image">
                <img :src="service.image" :alt="'Vorschaubild für ' + service.title + ' Service'" />
                <div class="service-overlay" aria-hidden="true">
                    <p class="hover-info-text">{{ service.hoverInfo }}</p>
                </div>
            </div>
            <footer class="service-footer">
                <span>Service öffnen</span>
                <svg class="external-link" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                    <polyline points="15,3 21,3 21,9"/>
                    <line x1="10" y1="14" x2="21" y2="3"/>
                </svg>
            </footer>
        </article>
    `,
    methods: {
        handleClick() {
            this.$emit('click', this.service);
        }
    }
};

const app = createApp({
    components: {
        ServiceCard
    },
    data() {
        return {
            services: [
                {
                    id: 1,
                    title: "IT-Anforderung",
                    description: "Erstellung von Tickets für IT-Anforderungen",
                    link: "#",
                    category: 'it-old',
                    recommended: false,
                    priority: false,
                    iconPaths: ["M12 20h9", "M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"],
                    hoverInfo: "Erfassung von Änderungswünschen, Funktionserweiterungen und Softwarebedarf.",
                    image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=200&fit=crop"
                },
                {
                    id: 2,
                    title: "Ideenmanagement",
                    description: "Ideen entwickeln und einbringen",
                    link: "https://ideenet.prod01.dakintra.de/ideenet/start.aspx?auto=1",
                    category: 'org-old',
                    recommended: false,
                    priority: false,
                    iconPaths: ["M9 12l2 2 4-4", "M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z", "M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z", "M12 21c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z", "M12 3c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z", "M18.364 18.364c.39.39 1.024.39 1.414 0s.39-1.024 0-1.414-.39-1.024-1.414 0-1.024.39 0 1.414z"],
                    hoverInfo: "Einbringung neuer Ideen zu Produkten, Prozessen und Werkzeugen.",
                    image: "https://images.unsplash.com/photo-1553484771-371a605b060b?w=300&h=200&fit=crop"
                },
                {
                    id: 3,
                    title: "Travelmanagement",
                    description: "Verwaltung von Reisen und Spesen",
                    link: "https://phs.sap.dak-sapcloud.de:44300/sap/bD1kZSZjPTEwMA==/bc/bsp/sap/ztm_framework/index1.htm",
                    category: 'org-old',
                    recommended: false,
                    priority: false,
                    iconPaths: ["M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z", "M16 13l-8 0", "M16 17l-8 0"],
                    hoverInfo: "Buchung und Dokumentation von Geschäftsreisen.",
                    image: "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=300&h=200&fit=crop"
                },
                {
                    id: 4,
                    title: "PC-Berechtigungen",
                    description: "Anfragen zu System -und Nutzerzugängen",
                    link: "https://apps.sharepoint.dakintra.de/PCBerechtigungen",
                    category: 'it-old',
                    recommended: true,
                    priority: true,
                    iconPaths: ["M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "M12 1l0 6", "M12 7l0 6", "M12 13l0 6", "M21 12l-6 0", "M15 12l-6 0", "M9 12l-6 0"],
                    hoverInfo: "Beantragung von System- und Sicherheitszugängen, sowie Nutzerfreigaben.",
                    image: "https://images.unsplash.com/photo-1563206767-5b18f218e8de?w=300&h=200&fit=crop"
                },
                {
                    id: 5,
                    title: "Hardware bestellen",
                    description: "Neue Hardware für Ihren Arbeitsplatz anfordern",
                    link: "https://servicenow.dak.de/dak",
                    category: 'it-old',
                    recommended: true,
                    priority: false,
                    iconPaths: ["M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"],
                    hoverInfo: "Beantragung von Laptops, Monitoren, Telefonen und sonstiges PC-Zubehör.",
                    image: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=300&h=200&fit=crop"
                },
                {
                    id: 6,
                    title: "Serviceformulare",
                    description: "Zugang zu den Formularen für DAK Services",
                    link: "https://spapps.sharepoint.dakintra.de/sites/formulare/",
                    category: 'org-old',
                    recommended: false,
                    priority: false,
                    iconPaths: ["M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z", "M16 13l-8 0", "M16 17l-8 0"],
                    hoverInfo: "Einreichung verschiedener Formulare im Kontext von HR, IT und administrativen Prozessen.",
                    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=300&h=200&fit=crop"
                },
                {
                    id: 7,
                    title: "Homeoffice-Antrag",
                    category: 'org-old',
                    recommended: false,
                    priority: false,
                    description: "Antrag auf Mitarbeiterreise",
                    link: "https://servicenow.dak.de/sp",
                    iconPaths: ["M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z", "M9 22l0 -10l6 0l0 10"],
                    hoverInfo: "Erstellung von Anträgen auf Homeoffice ( Mitarbeiterreise).",
                    image: "https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?w=300&h=200&fit=crop"
                },
                {
                    id: 8,
                    title: "bit-Chat und IT-Status",
                    category: 'it-old',
                    recommended: false,
                    priority: false,
                    description: "Direktzugang zur Bitmarck",
                    link: "https://dakq.sharepoint.com/sites/intra-1004",
                    iconPaths: ["M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"],
                    hoverInfo: "Zugang zu bit-Chat und allen Services die von der Bitmarck bereitgestellt werden. Beispiel: Nutzerkontensperrung ...",
                    image: "https://images.unsplash.com/photo-1577563908411-5077b6dc7624?w=300&h=200&fit=crop"
                },
                {
                    id: 9,
                    title: "Mobiliarbestellung",
                    category: 'org-old',
                    recommended: false,
                    priority: false,
                    description: "Bestellung von Mobilar und Einrichtung",
                    link: "mailto:<EMAIL>?subject=%23Mobiliarbestellung",
                    iconPaths: ["M2 11l20 0l0 9a2 2 0 0 1 -2 2l-16 0a2 2 0 0 1 -2 -2z", "M7 11l0 -4a5 5 0 0 1 10 0l0 4"],
                    hoverInfo: "Beantragung von Mobiliar und Bürozubehör.",
                    image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop"
                },
                {
                    id: 10,
                    invert:true,
                    title: "How to",
                    category: 'org-old',
                    recommended: false,
                    priority: false,
                    description: "Erklärung zu den Funktionalitäten",
                    link: "mailto:<EMAIL>?subject=%23Mobiliarbestellung",
                    iconPaths: ["M2 11l20 0l0 9a2 2 0 0 1 -2 2l-16 0a2 2 0 0 1 -2 -2z", "M7 11l0 -4a5 5 0 0 1 10 0l0 4"],
                    hoverInfo: "Erläuterung was sich hinter den Auswahlmöglichkeiten verbirgt und wie Anforderungen eingereicht werden.",
                    image: "https://images.unsplash.com/photo-1524758631624-e2822e304c36?w=300&h=200&fit=crop"
                }
            // Additional services from new design
            ,
                {
                    id: 11,
                    title: "Service‑Hilfe",
                    description: "Anleitungen und FAQ zu allen Services",
                    link: "#",
                    category: 'help',
                    recommended: true,
                    priority: false,
                    iconPaths: ["M12 2a10 10 0 1 0 0 20a10 10 0 0 0 0-20z", "M12 14v-.5c0-1.5 2-1.5 2-3.5c0-1.38-1.12-2.5-2.5-2.5S9 8.12 9 9.5", "M12 17h.01"],
                    hoverInfo: "Häufige Fragen und Nutzungsanleitungen",
                    image: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=300&h=200&fit=crop"
                },
                { id: 12, title: "Mobilgerät", description: "Smartphone oder Tablet anfordern", link: "#", category: 'it', recommended: false, priority: false, iconPaths: ["M7 2h10a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"], hoverInfo: "Gerätebereitstellung", image: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=200&fit=crop" },
                { id: 13, title: "Drucker Setup", description: "Drucker konfigurieren lassen", link: "#", category: 'it', recommended: false, priority: false, iconPaths: ["M6 9V2h12v7", "M6 13h12v8H6z", "M6 17h12"], hoverInfo: "Druckereinrichtung", image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=200&fit=crop" },
                { id: 14, title: "Software Installation", description: "Programme installieren lassen", link: "#", category: 'it', recommended: false, priority: false, iconPaths: ["M12 3v12", "M5 12l7 7l7-7"], hoverInfo: "Installationsanfrage", image: "https://images.unsplash.com/photo-1518779578993-ec3579fee39f?w=300&h=200&fit=crop" },
                { id: 15, title: "Störung melden", description: "Technische Probleme melden", link: "#", category: 'it', recommended: false, priority: false, iconPaths: ["M12 9v4", "M12 17h.01", "M3 12a9 9 0 1 0 18 0a9 9 0 0 0-18 0z"], hoverInfo: "Incident melden", image: "https://images.unsplash.com/photo-1525182008055-f88b95ff7980?w=300&h=200&fit=crop" },
                { id: 16, title: "Arbeitsplatz", description: "Büroausstattung anfordern", link: "#", category: 'org', recommended: false, priority: false, iconPaths: ["M4 6h16v4H4z", "M6 10v10", "M18 10v10"], hoverInfo: "Schreibtisch, Monitor, etc.", image: "https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=300&h=200&fit=crop" },
                { id: 17, title: "Raumbuchung", description: "Besprechungsräume buchen", link: "#", category: 'org', recommended: false, priority: false, iconPaths: ["M3 7h18", "M7 3v18"], hoverInfo: "Meetingräume", image: "https://images.unsplash.com/photo-1524758631624-e2822e304c36?w=300&h=200&fit=crop" },
                { id: 18, title: "Mitarbeiterausweis", description: "Neuen Ausweis beantragen", link: "#", category: 'org', recommended: false, priority: false, iconPaths: ["M4 4h16v6H4z", "M8 14h8", "M8 18h6"], hoverInfo: "Badge / Zutritt", image: "https://images.unsplash.com/photo-1543269865-cbf427effbad?w=300&h=200&fit=crop" },
                { id: 19, title: "Parkplatz", description: "Parkplatz beantragen", link: "#", category: 'org', recommended: false, priority: false, iconPaths: ["M3 16v-2a4 4 0 0 1 4-4h10a3 3 0 0 1 3 3v3", "M7 16h10", "M6 20h2", "M16 20h2"], hoverInfo: "Stellplatz", image: "https://images.unsplash.com/photo-1493238792000-8113da705763?w=300&h=200&fit=crop" }
            ]
        };
    },
    computed: {
        recommendedServices() {
            return this.services.filter(s => s.recommended).slice(0, 3);
        },
        itServices() {
            return this.services.filter(s => s.category === 'it' && !s.recommended);
        },
        orgServices() {
            return this.services.filter(s => s.category === 'org' && !s.recommended);
        }
    },
    methods: {
        handleServiceClick(service) {
            if (service.id === 1) {
                window.location.href = 'it-request.html';
                return;
            }
            
            if (service.link && service.link !== '#') {
                const event = arguments[1] || window.event;
                if (event && event.target) {
                    const card = event.target.closest('.service-card');
                    if (card) {
                        card.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            card.style.transform = '';
                        }, 150);
                    }
                }

                if (service.link.startsWith('mailto:')) {
                    window.location.href = service.link;
                } else {
                    window.open(service.link, '_blank', 'noopener,noreferrer');
                }
            } else {
                this.showNotification('Dieser Service ist noch nicht verfügbar.', 'info');
            }
        },


        
        showNotification(message, type = 'info') {
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());
            
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;

            Object.assign(notification.style, {
                position: 'fixed',
                top: '20px',
                right: '20px',
                background: type === 'info' ? 'rgba(255, 138, 0, 0.9)' : 'rgba(239, 68, 68, 0.9)',
                color: 'white',
                padding: '12px 20px',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                zIndex: '1000',
                fontSize: '14px',
                fontWeight: '500',
                transform: 'translateX(100%)',
                transition: 'transform 0.3s ease'
            });
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
    },
    
    mounted() {
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' || event.key === ' ') {
                const focusedElement = document.activeElement;
                if (focusedElement.classList.contains('service-card')) {
                    event.preventDefault();
                }
            }
            

        });
    }
});

app.mount('#app');