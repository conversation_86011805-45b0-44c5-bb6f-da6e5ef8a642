const { createApp } = Vue;

const app = createApp({
    data() {
        return {
            itRequestForm: {
                titel: '',
                name: '',
                kontakt: '',
                department: '',
                phone: '',
                originalRequester: '',
                artDerAnforderung: '',
                sonstigeText: '',
                shortDescription: '',
                priority: '',
                ursache: '',
                istSituation: '',
                sollSituation: '',
                mehrwert: '',
                umsetzungstermin: '',
                notes: ''
            },
            showTooltip: {
                ursache: false,
                istSituation: false,
                sollSituation: false
            },
            errors: {}
        };
    },
    methods: {
        submitITRequest() {
            // Clear previous errors
            this.errors = {};

            // Validate required fields
            const requiredFields = [
                { field: 'name', name: 'Name' },
                { field: 'kontakt', name: 'E‑Mail' },
                { field: 'artDerAnforderung', name: 'Art der Anforderung' },
                { field: 'shortDescription', name: 'Kurzbeschreibung' },
                { field: 'priority', name: 'Priorität' },
                { field: 'istSituation', name: 'Ausgangssituation (Ist)' },
                { field: 'sollSituation', name: 'Gewünschter Zielzustand' }
            ];

            const missingFields = requiredFields.filter(field => 
                !this.itRequestForm[field.field] || String(this.itRequestForm[field.field]).trim() === ''
            );

            // Inline errors
            missingFields.forEach(({ field, name }) => {
                this.errors[field] = `${name} ist erforderlich.`;
            });

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (this.itRequestForm.kontakt && !emailRegex.test(this.itRequestForm.kontakt)) {
                this.errors.kontakt = 'Bitte geben Sie eine gültige E‑Mail-Adresse ein.';
            }

            if (Object.keys(this.errors).length > 0) {
                this.showNotification('Bitte korrigieren Sie die markierten Felder.', 'error');
                const firstKey = Object.keys(this.errors)[0];
                const firstEl = document.getElementById(firstKey);
                if (firstEl) {
                    firstEl.focus();
                    firstEl.setAttribute('aria-invalid', 'true');
                }
                return;
            }

            const formData = { ...this.itRequestForm };

            const subject = formData.shortDescription || formData.titel || 'Neue IT‑Anforderung';
            const emailBody = `IT-Anforderung: ${subject}

Name: ${formData.name}
E-Mail: ${formData.kontakt}
Abteilung: ${formData.department || '-'}
Telefon: ${formData.phone || '-'}
${formData.originalRequester ? `Ursprünglich anfordernde Person: ${formData.originalRequester}` : ''}

Art der Anforderung: ${formData.artDerAnforderung}${formData.artDerAnforderung==='Sonstige' && formData.sonstigeText ? ` - ${formData.sonstigeText}` : ''}
Kurzbeschreibung: ${formData.shortDescription}
Priorität: ${formData.priority}

Ursache/Hintergrund:
${formData.ursache || '-'}

Ausgangssituation (Ist):
${formData.istSituation}

Gewünschter Zielzustand (Soll):
${formData.sollSituation}

Nutzen/Mehrwert:
${formData.mehrwert || '-'}

Gewünschter Umsetzungstermin: ${formData.umsetzungstermin || '-'}

Weitere Anmerkungen:
${formData.notes || '-'}
`;

            const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent('IT-Anforderung: ' + subject)}&body=${encodeURIComponent(emailBody)}`;
            
            window.location.href = mailtoLink;
            
            this.showNotification('E-Mail-Client wird geöffnet...', 'success');
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        },

        resetForm() {
            this.itRequestForm = {
                titel: '',
                name: '',
                kontakt: '',
                department: '',
                phone: '',
                originalRequester: '',
                artDerAnforderung: '',
                sonstigeText: '',
                shortDescription: '',
                priority: '',
                ursache: '',
                istSituation: '',
                sollSituation: '',
                mehrwert: '',
                umsetzungstermin: '',
                notes: ''
            };
            this.showTooltip = {
                ursache: false,
                istSituation: false,
                sollSituation: false
            };
            this.errors = {};
        },
        
        showNotification(message, type = 'info') {
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());
            
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
            notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

            const colors = {
                info: 'rgba(239, 125, 0, 0.95)',
                success: 'rgba(34, 197, 94, 0.95)',
                error: 'rgba(239, 68, 68, 0.95)'
            };

            Object.assign(notification.style, {
                position: 'fixed',
                top: '20px',
                right: '20px',
                background: colors[type] || colors.info,
                color: 'white',
                padding: '16px 24px',
                borderRadius: '12px',
                boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                zIndex: '1000',
                fontSize: '14px',
                fontWeight: '500',
                transform: 'translateX(100%)',
                transition: 'transform 0.3s ease',
                maxWidth: '300px'
            });
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
    },
    
    mounted() {
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                if (confirm('Möchten Sie das Formular wirklich verlassen? Alle eingegebenen Daten gehen verloren.')) {
                    window.location.href = 'index.html';
                }
            }
        });

        const inputs = document.querySelectorAll('.form-input');
        inputs.forEach(input => {
            input.addEventListener('focus', (e) => {
                const group = e.target.closest('.form-group');
                if (group) group.style.transform = 'translateY(-2px)';
                // Clear any previous validation errors
                e.target.removeAttribute('aria-invalid');
            });
            
            input.addEventListener('blur', (e) => {
                const group = e.target.closest('.form-group');
                if (group) group.style.transform = '';
            });

            // Add real-time validation for required fields
            input.addEventListener('input', (e) => {
                if (e.target.hasAttribute('required') && e.target.value.trim() !== '') {
                    e.target.removeAttribute('aria-invalid');
                }
            });
        });

        // Textareas: collapsed (1 row) by default, expand to 5 rows on focus, collapse on blur
        const textareas = document.querySelectorAll('textarea.form-textarea');
        textareas.forEach((ta) => {
            try { ta.setAttribute('rows', '1'); } catch (e) {}
            ta.addEventListener('focus', () => {
                ta.setAttribute('rows', '5');
            });
            ta.addEventListener('blur', () => {
                ta.setAttribute('rows', '1');
            });
        });

        // Add keyboard navigation for info buttons
        const infoButtons = document.querySelectorAll('.info-icon');
        infoButtons.forEach(button => {
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    // Toggle tooltip visibility
                    const tooltipId = button.getAttribute('aria-describedby');
                    if (tooltipId) {
                        const tooltip = document.getElementById(tooltipId);
                        if (tooltip) {
                            const isVisible = tooltip.style.display !== 'none';
                            tooltip.style.display = isVisible ? 'none' : 'block';
                        }
                    }
                }
            });
        });
    }
});

app.mount('#app');