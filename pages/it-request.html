<!DOCTYPE html>
<html lang="de-DE">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT-Anforderung - DAK Service Portal</title>
    <link rel="stylesheet" href="../styles/styles.css">
    <link rel="stylesheet" href="../styles/it-request.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app" class="mesh-box">
        <!-- Main Content -->
        <main class="main-content" id="main-content" role="main">
            <div class="form-container">
                <section class="form-card" aria-labelledby="form-heading">
<header class="form-header">
                        <nav class="breadcrumbs" aria-label="Breadcrumb">
                            <ol>
                                <li><a href="#">Dashboard</a></li>
                                <li><a href="#">IT Requests</a></li>
                                <li aria-current="page">New Request</li>
                            </ol>
                        </nav>
                        <h1 id="form-heading">IT‑Anforderung Formular</h1>
                        <p class="form-intro">Submit your IT solution request with detailed requirements and justification.</p>
                    </header>

<form class="it-request-form" @submit.prevent="submitITRequest" novalidate aria-describedby="form-description">
                        <div id="form-description" class="sr-only">Formular zur Erstellung einer IT-Anforderung mit Pflichtfeldern und optionalen Angaben</div>

                        <!-- Kontaktinformationen -->
                        <section class="form-set" aria-labelledby="set-contact">
<div class="form-set-head">
                                <span class="form-set-icon" aria-hidden="true">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                                        <circle cx="12" cy="7" r="4"/>
                                        <path d="M4 20v-1c0-3 4-5 8-5s8 2 8 5v1"/>
                                    </svg>
                                </span>
                                <h2 id="set-contact" class="form-set-title">Kontaktinformationen</h2>
                            </div>
                            <div class="form-set-grid">
                                <div class="form-group">
                                    <label class="form-label" for="name">Name *</label>
                                    <input id="name" v-model="itRequestForm.name" type="text" class="form-input" required aria-required="true" placeholder="Ihr vollständiger Name" />
                                    <div class="field-error" v-if="errors.name">{{ errors.name }}</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="kontakt">E‑Mail *</label>
                                    <input id="kontakt" v-model="itRequestForm.kontakt" type="email" class="form-input" required aria-required="true" aria-describedby="kontakt-help" placeholder="<EMAIL>" />
                                    <div class="field-error" v-if="errors.kontakt">{{ errors.kontakt }}</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="department">Abteilung</label>
                                    <select id="department" v-model="itRequestForm.department" class="form-input">
                                        <option value="">Abteilung auswählen</option>
                                        <option>IT</option>
                                        <option>HR</option>
                                        <option>Finance</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="phone">Telefon</label>
                                    <input id="phone" v-model="itRequestForm.phone" type="tel" class="form-input" placeholder="+49 123 456789" />
                                </div>
                            </div>
                        </section>

                        <!-- Anforderungsdetails -->
                        <section class="form-set" aria-labelledby="set-details">
<div class="form-set-head">
                            <span class="form-set-icon" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                                    <rect x="4" y="3" width="16" height="18" rx="2"/>
                                    <path d="M8 7h8M8 11h8M8 15h8"/>
                                </svg>
                            </span>
                            <h2 id="set-details" class="form-set-title">Anforderungsdetails</h2>
                        </div>
                            <div class="form-group">
                                <label class="form-label">Art der Anforderung *</label>
                                <div class="radio-group radio-inline" role="radiogroup" aria-required="true" aria-describedby="art-help">
                                    <label class="radio-label"><input type="radio" name="artDerAnforderung" value="Neue Software" v-model="itRequestForm.artDerAnforderung" required aria-describedby="neue-software-desc" /> <span>Neue Software</span></label>
                                    <label class="radio-label"><input type="radio" name="artDerAnforderung" value="Anpassung" v-model="itRequestForm.artDerAnforderung" required aria-describedby="anpassung-desc" /> <span>Anpassung</span></label>
                                    <label class="radio-label"><input type="radio" name="artDerAnforderung" value="Hardware" v-model="itRequestForm.artDerAnforderung" required aria-describedby="hardware-desc" /> <span>Hardware</span></label>
                                </div>
                                <div v-if="itRequestForm.artDerAnforderung==='Sonstige'" class="form-group" style="margin-top:.5rem;">
                                    <input id="sonstigeText" v-model="itRequestForm.sonstigeText" type="text" class="form-input" placeholder="Bitte beschreiben..." />
                                </div>
                                <div class="field-error" v-if="errors.artDerAnforderung">{{ errors.artDerAnforderung }}</div>
                            </div>
                            <div class="form-set-grid">
                                <div class="form-group" style="grid-column: 1 / -1;">
                                    <label class="form-label" for="shortDescription">Kurzbeschreibung *</label>
                                    <input id="shortDescription" v-model="itRequestForm.shortDescription" class="form-input" type="text" placeholder="Kurze Zusammenfassung Ihrer Anforderung" required />
                                    <div class="field-error" v-if="errors.shortDescription">{{ errors.shortDescription }}</div>
                                </div>
                                <div class="form-group" style="max-width: 360px;">
                                    <label class="form-label" for="priority">Priorität *</label>
                                    <select id="priority" v-model="itRequestForm.priority" class="form-input" required>
                                        <option value="">Priorität auswählen</option>
                                        <option>Niedrig</option>
                                        <option>Normal</option>
                                        <option>Hoch</option>
                                        <option>Prio</option>
                                    </select>
                                    <div class="field-error" v-if="errors.priority">{{ errors.priority }}</div>
                                </div>
                            </div>
                        </section>

                        <!-- Begründung & Hintergrund -->
                        <section class="form-set" aria-labelledby="set-background">
<div class="form-set-head">
                            <span class="form-set-icon" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                                    <path d="M9 18h6M10 22h4"/>
                                    <path d="M12 2a7 7 0 0 1 7 7c0 2.5-1.5 4-3 5-1 1-1 2-1 3H9c0-1 0-2-1-3-1.5-1-3-2.5-3-5a7 7 0 0 1 7-7z"/>
                                </svg>
                            </span>
                            <h2 id="set-background" class="form-set-title">Begründung & Hintergrund</h2>
                        </div>
                            <div class="form-group">
                                <div class="label-with-info">
                                    <label class="form-label" for="ursache">Ursache / Hintergrund</label>
                                    <div class="info-icon-container">
                                        <button type="button" class="info-icon" @mouseenter="showTooltip.ursache = true" @mouseleave="showTooltip.ursache = false" @focus="showTooltip.ursache = true" @blur="showTooltip.ursache = false" aria-describedby="ursache-tooltip" aria-label="Hilfe zu Ursache anzeigen">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                        </button>
                                        <div id="ursache-tooltip" class="tooltip" v-show="showTooltip.ursache" role="tooltip">Beschreiben Sie den aktuellen Zustand und warum eine Änderung notwendig ist.</div>
                                    </div>
                                </div>
                                <textarea id="ursache" v-model="itRequestForm.ursache" class="form-input form-textarea" placeholder="Beschreibung der Ursache" rows="1" aria-describedby="ursache-help"></textarea>
                                <div class="field-error" v-if="errors.ursache">{{ errors.ursache }}</div>
                            </div>
                            <div class="form-group">
                                <div class="label-with-info">
                                    <label class="form-label" for="istSituation">Ausgangssituation (Ist) *</label>
                                    <div class="info-icon-container">
                                        <button type="button" class="info-icon" @mouseenter="showTooltip.istSituation = true" @mouseleave="showTooltip.istSituation = false" @focus="showTooltip.istSituation = true" @blur="showTooltip.istSituation = false" aria-describedby="ist-tooltip" aria-label="Hilfe zur Ausgangssituation anzeigen">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                        </button>
                                        <div id="ist-tooltip" class="tooltip" v-show="showTooltip.istSituation" role="tooltip">Beschreiben Sie die aktuelle Situation und den bestehenden Prozess detailliert.</div>
                                    </div>
                                </div>
                                <textarea id="istSituation" v-model="itRequestForm.istSituation" class="form-input form-textarea" required aria-required="true" aria-describedby="ist-help" placeholder="Beschreibung der aktuellen Situation" rows="1"></textarea>
                                <div class="field-error" v-if="errors.istSituation">{{ errors.istSituation }}</div>
                            </div>
                            <div class="form-group">
                                <div class="label-with-info">
                                    <label class="form-label" for="sollSituation">Gewünschter Zielzustand *</label>
                                    <div class="info-icon-container">
                                        <button type="button" class="info-icon" @mouseenter="showTooltip.sollSituation = true" @mouseleave="showTooltip.sollSituation = false" @focus="showTooltip.sollSituation = true" @blur="showTooltip.sollSituation = false" aria-describedby="soll-tooltip" aria-label="Hilfe zur Zielstellung anzeigen">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                        </button>
                                        <div id="soll-tooltip" class="tooltip" v-show="showTooltip.sollSituation" role="tooltip">Beschreiben Sie die gewünschte Zielstellung.</div>
                                    </div>
                                </div>
                                <textarea id="sollSituation" v-model="itRequestForm.sollSituation" class="form-input form-textarea" required aria-required="true" aria-describedby="soll-help" placeholder="Beschreibung der gewünschten Situation" rows="1"></textarea>
                                <div class="field-error" v-if="errors.sollSituation">{{ errors.sollSituation }}</div>
                            </div>
                            <div class="form-set-grid">
                                <div class="form-group" style="grid-column: 1 / -1;">
                                    <label class="form-label" for="mehrwert">Nutzen / Mehrwert</label>
                                    <textarea id="mehrwert" v-model="itRequestForm.mehrwert" class="form-input form-textarea" placeholder="Welchen konkreten Nutzen erwarten Sie?" rows="1"></textarea>
                                </div>
                                <div class="form-group" style="max-width: 360px;">
                                    <label class="form-label" for="umsetzungstermin">Gewünschter Umsetzungstermin</label>
                                    <input id="umsetzungstermin" v-model="itRequestForm.umsetzungstermin" type="date" class="form-input datepicker-german" />
                                </div>
                            </div>
                        </section>

                        <!-- Zusätzliche Informationen -->
                        <section class="form-set" aria-labelledby="add-info-heading">
<div class="form-set-head">
                            <span class="form-set-icon" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                                    <path d="M8 12l8-8m-6 14l8-8a3 3 0 1 0-4.24-4.24l-8 8a5 5 0 0 0 7.07 7.07l4.24-4.24"/>
                                </svg>
                            </span>
                            <h2 id="add-info-heading" class="form-set-title">Zusätzliche Informationen</h2>
                        </div>
                            <div class="form-group drop-placeholder">
                                <label class="form-label" for="anhang">Anhänge</label>
                                <div class="form-input" style="text-align:center; color:#6b7280;">
                                    Dateien hier ablegen oder Dateien auswählen (Demo‑Platzhalter)
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="notes">Weitere Anmerkungen</label>
                                <textarea id="notes" v-model="itRequestForm.notes" class="form-input form-textarea" placeholder="Zusätzliche Informationen oder spezielle Anforderungen..." rows="1"></textarea>
                            </div>
                        </section>
                            <!-- Left Column -->
                            <div class="form-column" role="group" aria-labelledby="basic-info-heading">
                                <h2 id="basic-info-heading" class="sr-only">Grundlegende Informationen</h2>
<div class="form-group">
                                    <label class="form-label" for="titel">Titel *</label>
                                    <input 
                                        id="titel"
                                        v-model="itRequestForm.titel" 
                                        type="text" 
                                        class="form-input" 
                                        required
                                        aria-required="true"
                                        aria-describedby="titel-help"
                                        placeholder="Kurzer Titel der Anforderung"
                                    />
                                    <div class="field-error" v-if="errors.titel">{{ errors.titel }}</div>
                                    <div id="titel-help" class="sr-only">Geben Sie einen kurzen, aussagekräftigen Titel für Ihre IT-Anforderung ein</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="name">Name *</label>
                                    <input id="name" v-model="itRequestForm.name" type="text" class="form-input" required aria-required="true" placeholder="Ihr vollständiger Name" />
                                    <div class="field-error" v-if="errors.name">{{ errors.name }}</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="kontakt">E‑Mail *</label>
                                    <input 
                                        id="kontakt"
                                        v-model="itRequestForm.kontakt" 
                                        type="email" 
                                        class="form-input" 
                                        required
                                        aria-required="true"
                                        aria-describedby="kontakt-help"
                                        placeholder="<EMAIL>"
                                    />
                                    <div class="field-error" v-if="errors.kontakt">{{ errors.kontakt }}</div>
                                    <div id="kontakt-help" class="sr-only">Geben Sie Ihre E-Mail-Adresse für Rückfragen ein</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="department">Abteilung</label>
                                    <select id="department" v-model="itRequestForm.department" class="form-input">
                                        <option value="">Abteilung auswählen</option>
                                        <option>IT</option>
                                        <option>HR</option>
                                        <option>Finance</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="phone">Telefon</label>
                                    <input id="phone" v-model="itRequestForm.phone" type="tel" class="form-input" placeholder="+49 123 456789" />
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="originalRequester">Kontaktdaten der ursprünglich anfordernden Person</label>
                                    <input 
                                        id="originalRequester"
                                        v-model="itRequestForm.originalRequester" 
                                        type="text" 
                                        class="form-input"
                                        aria-describedby="original-requester-help"
                                        placeholder="Name und E-Mail des/r ursprünglichen Anforderers"
                                    />
                                    <div id="original-requester-help" class="sr-only">Optionale Angabe, falls Sie die Anforderung für eine andere Person stellen</div>
                                </div>

                                <fieldset class="form-group">
                                    <label class="form-label">Art der Anforderung *</label>
                                    <div class="radio-group" role="radiogroup" aria-required="true" aria-describedby="art-help">
                                        <label class="radio-label">
                                            <input type="radio" name="artDerAnforderung" value="Neue Software" v-model="itRequestForm.artDerAnforderung" required aria-describedby="neue-software-desc" />
                                            <span>Neue Software</span>
                                        </label>
                                        <label class="radio-label">
                                            <input type="radio" name="artDerAnforderung" value="Anpassung" v-model="itRequestForm.artDerAnforderung" required aria-describedby="anpassung-desc" />
                                            <span>Anpassung</span>
                                        </label>
                                        <label class="radio-label">
                                            <input type="radio" name="artDerAnforderung" value="Hardware" v-model="itRequestForm.artDerAnforderung" required aria-describedby="hardware-desc" />
                                            <span>Hardware</span>
                                        </label>
                                        <label class="radio-label">
                                            <input type="radio" name="artDerAnforderung" value="Sonstige" v-model="itRequestForm.artDerAnforderung" required aria-describedby="sonstige-desc" />
                                            <span>Sonstige</span>
                                        </label>
                                    </div>
                                    <div v-if="itRequestForm.artDerAnforderung==='Sonstige'" class="form-group" style="margin-top:.5rem;">
                                        <input id="sonstigeText" v-model="itRequestForm.sonstigeText" type="text" class="form-input" placeholder="Bitte beschreiben..." />
                                    </div>
                                    <div class="field-error" v-if="errors.artDerAnforderung">{{ errors.artDerAnforderung }}</div>
                                    <div id="art-help" class="sr-only">Wählen Sie die Art Ihrer IT-Anforderung aus</div>
                                </fieldset>

                                <div class="form-group">
                                    <label class="form-label" for="shortDescription">Kurzbeschreibung *</label>
                                    <input id="shortDescription" v-model="itRequestForm.shortDescription" class="form-input" type="text" placeholder="Kurze Zusammenfassung Ihrer Anforderung" required />
                                    <div class="field-error" v-if="errors.shortDescription">{{ errors.shortDescription }}</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="priority">Priorität *</label>
                                    <select id="priority" v-model="itRequestForm.priority" class="form-input" required>
                                        <option value="">Priorität auswählen</option>
                                        <option>Niedrig</option>
                                        <option>Normal</option>
                                        <option>Hoch</option>
                                        <option>Prio</option>
                                    </select>
                                    <div class="field-error" v-if="errors.priority">{{ errors.priority }}</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="nutzen">Nutzen *</label>
                                    <div class="nutzen-container">
                                        <select 
                                            id="nutzen"
                                            v-model="itRequestForm.nutzen" 
                                            class="form-input" 
                                            required
                                            aria-required="true"
                                            aria-describedby="nutzen-help"
                                        >
                                            <option value="">Bitte wählen...</option>
                                            <option v-for="option in nutzenOptions" :key="option" :value="option">
                                                {{ option }}
                                            </option>
                                        </select>
                                        <div class="field-error" v-if="errors.nutzen">{{ errors.nutzen }}</div>
                                        <div id="nutzen-help" class="sr-only">Wählen Sie den erwarteten Nutzen Ihrer IT-Anforderung aus</div>
                                        <div v-show="itRequestForm.nutzen === 'Gesetzlich'" class="gesetzlich-datum">
                                            <label class="form-label" for="gesetzlichDatum">Gesetzliche Fälligkeit *</label>
                                            <input 
                                                id="gesetzlichDatum"
                                                v-model="itRequestForm.gesetzlichDatum" 
                                                type="date" 
                                                class="form-input datepicker-german"
                                                aria-describedby="faelligkeit-help"
                                                :required="itRequestForm.nutzen === 'Gesetzlich'"
                                                :aria-required="itRequestForm.nutzen === 'Gesetzlich'"
                                            />
                                            <div id="faelligkeit-help" class="sr-only">Geben Sie das Datum der gesetzlichen Fälligkeit an</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="form-column" role="group" aria-labelledby="detailed-info-heading">
                                <h2 id="detailed-info-heading" class="sr-only">Detaillierte Beschreibung</h2>
                                <div class="form-group">
                                    <div class="label-with-info">
                                        <label class="form-label" for="ursache">Ursache / Hintergrund *</label>
                                        <div class="info-icon-container">
                                            <button type="button" class="info-icon" @mouseenter="showTooltip.ursache = true" @mouseleave="showTooltip.ursache = false" @focus="showTooltip.ursache = true" @blur="showTooltip.ursache = false" aria-describedby="ursache-tooltip" aria-label="Hilfe zu Ursache anzeigen">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                            </button>
                                            <div id="ursache-tooltip" class="tooltip" v-show="showTooltip.ursache" role="tooltip">Beschreiben Sie den aktuellen Zustand und warum eine Änderung notwendig ist.</div>
                                        </div>
                                    </div>
                                    <textarea id="ursache" v-model="itRequestForm.ursache" class="form-input form-textarea" placeholder="Beschreibung der Ursache" rows="1" aria-describedby="ursache-help"></textarea>
                                    <div class="field-error" v-if="errors.ursache">{{ errors.ursache }}</div>
                                    <div id="ursache-help" class="sr-only">Optionale Angabe zur Ursache der Anforderung</div>
                                </div>

                                <div class="form-group">
                                    <div class="label-with-info">
                                        <label class="form-label" for="istSituation">Ausgangssituation (Ist) *</label>
                                        <div class="info-icon-container">
                                            <button type="button" class="info-icon" @mouseenter="showTooltip.istSituation = true" @mouseleave="showTooltip.istSituation = false" @focus="showTooltip.istSituation = true" @blur="showTooltip.istSituation = false" aria-describedby="ist-tooltip" aria-label="Hilfe zur Ausgangssituation anzeigen">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                            </button>
                                            <div id="ist-tooltip" class="tooltip" v-show="showTooltip.istSituation" role="tooltip">Beschreiben Sie die aktuelle Situation und den bestehenden Prozess detailliert.</div>
                                        </div>
                                    </div>
                                    <textarea id="istSituation" v-model="itRequestForm.istSituation" class="form-input form-textarea" required aria-required="true" aria-describedby="ist-help" placeholder="Beschreibung der aktuellen Situation" rows="1"></textarea>
                                    <div class="field-error" v-if="errors.istSituation">{{ errors.istSituation }}</div>
                                    <div id="ist-help" class="sr-only">Pflichtfeld: Beschreiben Sie die aktuelle Ausgangssituation</div>
                                </div>

                                <div class="form-group">
                                    <div class="label-with-info">
                                        <label class="form-label" for="sollSituation">Gewünschter Zielzustand *</label>
                                        <div class="info-icon-container">
                                            <button type="button" class="info-icon" @mouseenter="showTooltip.sollSituation = true" @mouseleave="showTooltip.sollSituation = false" @focus="showTooltip.sollSituation = true" @blur="showTooltip.sollSituation = false" aria-describedby="soll-tooltip" aria-label="Hilfe zur Zielstellung anzeigen">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                            </button>
                                            <div id="soll-tooltip" class="tooltip" v-show="showTooltip.sollSituation" role="tooltip">Beschreiben Sie die gewünschte Zielstellung.</div>
                                        </div>
                                    </div>
                                    <textarea id="sollSituation" v-model="itRequestForm.sollSituation" class="form-input form-textarea" required aria-required="true" aria-describedby="soll-help" placeholder="Beschreibung der gewünschten Situation" rows="1"></textarea>
                                    <div class="field-error" v-if="errors.sollSituation">{{ errors.sollSituation }}</div>
                                    <div id="soll-help" class="sr-only">Pflichtfeld: Beschreiben Sie die gewünschte Zielstellung</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="mehrwert">Nutzen / Mehrwert</label>
                                    <textarea id="mehrwert" v-model="itRequestForm.mehrwert" class="form-input form-textarea" placeholder="Welchen konkreten Nutzen erwarten Sie?" rows="1"></textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="umsetzungstermin">Gewünschter Umsetzungstermin</label>
                                    <input id="umsetzungstermin" v-model="itRequestForm.umsetzungstermin" type="date" class="form-input datepicker-german" />
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions" role="group" aria-label="Formular-Aktionen">
                            <a href="index.html" class="btn btn-secondary" aria-label="Formular abbrechen und zur Startseite zurückkehren">Abbrechen</a>
                            <button type="submit" class="btn btn-primary" aria-describedby="submit-help">IT-Anforderung senden</button>
                            <div id="submit-help" class="sr-only">Sendet die IT-Anforderung per E-Mail an das IT-Support-Team</div>
                        </div>
                    </main>
                </section>
            </div>
        </main>
    </div>

    <script src="../scripts/it-request.js"></script>
</body>
</html>