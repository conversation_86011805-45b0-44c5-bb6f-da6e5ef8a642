/* IT Request Page Styles */

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Background and Layout */
body {
    margin: 0;
    padding: 20px;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #111827;
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
    position: relative;
    background-color: #f8fafc;
}

@keyframes backgroundFloat {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 0.6;
    }
}

/* Page header (Figma-aligned) */
.form-header { padding: 1rem 1rem 0 1rem; text-align: left; }
.breadcrumbs ol { list-style: none; padding: 0; margin: 0 0 .5rem; display: flex; align-items: center; flex-wrap: wrap; gap: 8px; font-size: 14px; line-height: 20px; }
.breadcrumbs li { display: inline-flex; align-items: center; color: #737373; }
.breadcrumbs a { color: #737373; text-decoration: none; }
.breadcrumbs li + li::before { content: "›"; color: #737373; margin: 0 8px; font-size: 14px; line-height: 12px; }
.breadcrumbs li[aria-current="page"] { color: #171717; }

.form-header h1 { font-size: 30px; line-height: 36px; font-weight: 400; color: #171717; margin: 16px 0 8px; position: static !important; transform: none !important; left: auto !important; text-align: left; }
.form-intro { color: #525252; font-size: 16px; line-height: 24px; margin: 0; }

.header-text p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.5rem;
    color: #3f3f3f;
    text-decoration: none;
    font-weight: 700;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    min-height: 44px;
}

.back-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.back-button svg {
    width: 0.875rem;
    height: 0.875rem;
}

/* Main Content */
.main-content {
    padding: 0.5rem;
    max-width: 1000px;
    margin: 0 auto;
}

/* Form Container - Liquid Glass Effect */
.form-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 40px);
}

.form-card {
    width: 100%;
    max-width: 900px;
    padding: 1rem;
    position: relative;
}

/* Form Header */
.form-header {
    margin-bottom: 1rem;
}

.form-header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    position: relative;
}

.form-header h1 {
    font-size: 1.4rem;
    color: #111827;
    margin: 0;
    position: absolute;
    transform: translateX(-50%);
}

.form-header p {
    font-size: 0.9rem;
    color: #6b7280;
    margin: 0;
}

/* Two Column Layout */
.form-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.form-column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Form set panels */
.form-set { background: #ffffff; border: 1px solid #e5e7eb; border-radius: 12px; padding: 1rem; margin-bottom: 1rem; }
.form-set-title { font-size: 1rem; font-weight: 700; color: #111827; margin: 0; }
.form-set-head { display: flex; align-items: center; gap: .5rem; margin-bottom: .75rem; }
.form-set-icon { width: 20px; height: 20px; color: #525252; display: inline-flex; align-items: center; justify-content: center; }
.form-set-icon svg { width: 20px; height: 20px; }
.form-set-grid { display: grid; grid-template-columns: 1fr 1fr; gap: .75rem 1rem; }

/* Inner groups are spacing only */
.form-group { position: relative; background: transparent; border: none; padding: 0; margin-bottom: .75rem; }

.form-group:hover { background: transparent; }

/* Keep input focus visible */
.form-group:focus-within { }

/* Form Labels */
.form-label,
fieldset legend.form-label {
    font-weight: 400;
    color: #111827;
    margin-bottom: 0.35rem;
    font-size: 0.9rem;
    position: relative;
    display: block;
}

fieldset {
    border: none;
    padding: 0;
    margin: 0;
}

fieldset legend {
    padding: 0;
}

.form-label::after,
fieldset legend.form-label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #9ca3af, transparent);
    border-radius: 1px;
}

/* Form Inputs - Glass Effect */
.form-input {
    width: 100%;
    padding: 0.55rem;
    background: rgba(255, 255, 255, 0.97);
    backdrop-filter: blur(12px);
    border: 1px solid #e6e6e6;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    color: #1f2937;
    transition: all 0.3s ease;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.04);
    min-height: 38px;
}

.form-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 1);
    border-color: rgba(239, 125, 0, 0.6);
    box-shadow: 
        0 0 0 2px rgba(239, 125, 0, 0.15),
        inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-input::placeholder {
    color: #6b7280;
}

.form-textarea {
    min-height: 38px;
    resize: vertical;
    font-family: inherit;
    line-height: 1.35;
    overflow-y: auto;
    transition: height 0.2s ease;
}

/* Radio Groups */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.radio-inline {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
    width: 100%;
    justify-content: space-between;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #111827;
    padding: 0.5rem 0.75rem;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.4rem;
    transition: background-color 0.2s ease, border-color 0.2s ease;
    text-shadow: none;
    min-height: 34px;
    flex: 1;
    min-width: 0;
}

.radio-label:hover {
    background: rgba(255, 255, 255, 1);
}

.radio-label input[type="radio"] {
    margin: 0;
    width: 1.2rem;
    height: 1.2rem;
}

/* Nutzen Container */
.nutzen-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.gesetzlich-datum {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    padding: 0.5rem;
    background: rgba(239, 125, 0, 0.08);
    border-radius: 0.5rem;
    border: 1px solid rgba(239, 125, 0, 0.25);
}

/* Info Icons and Tooltips */
.label-with-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.info-icon-container {
    position: relative;
}

.info-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    color: #EF7D00;
    cursor: pointer;
    border-radius: 50%;
    background: rgba(204, 102, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(204, 102, 0, 0.2);
    transition: all 0.3s ease;
}

.info-icon:hover {
    background: rgba(204, 102, 0, 0.2);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(204, 102, 0, 0.2);
}

.info-icon svg {
    width: 1rem;
    height: 1rem;
}

.tooltip {
    position: absolute;
    top: 50%;
    left: calc(100% + 1rem);
    transform: translateY(-50%);
    background: rgba(51, 51, 51, 0.95);
    backdrop-filter: blur(20px);
    color: white;
    padding: 1rem;
    border-radius: 0.75rem;
    font-size: 0.8rem;
    line-height: 1.4;
    width: 250px;
    z-index: 1001;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -0.5rem;
    transform: translateY(-50%);
    border-top: 0.5rem solid transparent;
    border-bottom: 0.5rem solid transparent;
    border-right: 0.5rem solid rgba(51, 51, 51, 0.95);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(255, 255, 255, 0.4);
}

.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 140px;
    min-height: 40px;
    backdrop-filter: blur(20px);
}

.btn-primary {
    background: linear-gradient(135deg, #EF7D00 0%, #ff6b00 50%, #EF7D00 100%);
    background-size: 200% 100%;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 8px 24px rgba(204, 102, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    animation: gradientMove 3s ease-in-out infinite;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 12px 32px rgba(204, 102, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    animation-duration: 1s;
}

.btn-secondary {
    background: transparent;
    color: #111827;
    border: 1px solid #e5e7eb;
    box-shadow: none;
}

.btn-secondary:hover {
    background: #f3f4f6;
}

@keyframes gradientMove {
    0%, 100% {
        background-position: 0% 0%;
    }
    50% {
        background-position: 100% 0%;
    }
}

/* Field errors */
.field-error { color: #b91c1c; font-size: .8rem; margin-top: .25rem; }

/* Focus and Keyboard Navigation Styles */
*:focus {
    outline: 3px solid #CC6600;
    outline-offset: 2px;
}

/* Drag/drop placeholder */
.drop-placeholder { border: 1px dashed #e5e7eb; border-radius: 8px; padding: .75rem; }

.form-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(204, 102, 0, 0.6);
    box-shadow: 
        0 0 0 3px rgba(204, 102, 0, 0.15),
        inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-button:focus,
.btn:focus {
    outline: 3px solid #CC6600;
    outline-offset: 2px;
}

.radio-label:focus-within {
    background: rgba(204, 102, 0, 0.15);
    outline: 2px solid #CC6600;
    outline-offset: 1px;
}

.info-icon:focus {
    outline: 2px solid #CC6600;
    outline-offset: 1px;
    background: rgba(204, 102, 0, 0.2);
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .form-columns {
        grid-template-columns: 1fr;
        gap: 1.05rem;
    }
    
    .form-card {
        padding: 1.0rem;
    }
    
    .main-content {
        padding: 0.25rem;
    }
}

@media (max-width: 768px) {
    .form-set-grid { grid-template-columns: 1fr; }

    .header-text h1 {
        font-size: 1.4rem;
    }

    .form-header-top {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .form-header h2 {
        font-size: 1.75rem;
        text-align: center;
        width: 100%;
    }

    .back-button {
        align-self: flex-start;
    }

    .form-card {
        padding: 1.25rem;
        border-radius: 1.25rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .btn {
        width: 100%;
    }

    .tooltip {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 90vw;
        width: auto;
    }

    .tooltip::before {
        display: none;
    }
}

/* Medium screens - stack radio buttons vertically */
@media (max-width: 768px) {
    .radio-inline {
        flex-direction: column;
        gap: 0.75rem;
    }

    .radio-label {
        flex: none;
        justify-content: flex-start;
        text-align: left;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .form-group {
        padding: 0.75rem;
    }

    .radio-group {
        gap: 0.5rem;
    }

    .radio-inline {
        flex-direction: column;
        gap: 0.5rem;
    }

    .radio-label {
        padding: 0.5rem;
        font-size: 0.85rem;
        flex: none;
        justify-content: flex-start;
        text-align: left;
    }

    .form-header {
        margin-bottom: 1.5rem;
    }

    .form-header h2 {
        font-size: 1.5rem;
    }

    .back-button {
        padding: 0.4rem 0.75rem;
        font-size: 0.8rem;
        gap: 0.25rem;
    }

    .back-button svg {
        width: 0.75rem;
        height: 0.75rem;
    }
}

/* Mesh Box Background Pattern */
.mesh-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}