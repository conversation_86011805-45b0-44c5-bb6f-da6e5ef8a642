/* DAK Landing V2 - neutral, accessible theme */

:root {
  --bg: #eef0f0;
  --surface: #ffffff;
  --muted: #6b7280;
  --text: #111827;
  --border: #e5e7eb;
  --ring: rgba(59,130,246,0.25);
  --cta: #111827;
  --highlight: #ef7d00;
}

body {
  background: var(--bg);
  color: var(--text);
}

.topbar {
  position: sticky;
  top: 0;
  z-index: 50;
  background: var(--surface);
  border-bottom: 1px solid var(--border);
}
.topbar-inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.brand { display:flex; align-items:center; gap: .5rem; }
.brand-logo { height: 28px; width: auto; }
.brand-title { font-weight: 700; letter-spacing: .2px; }
.search-input {
  width: 280px;
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: .5rem .75rem;
  font-size: 0.95rem;
}
.search-input:focus { outline: 3px solid var(--ring); border-color: #93c5fd; }

.layout { max-width: 1200px; margin: 0 auto; padding: 1rem; display: grid; grid-template-columns: 280px 1fr; gap: 1rem; }
.welcome { grid-column: 1 / -1; margin-bottom: .5rem; }
.sidebar { position: sticky; top: 72px; align-self: start; }
.panel { background: var(--surface); border: 1px solid var(--border); border-radius: 8px; padding: 1rem; box-shadow: 0 1px 2px rgba(0,0,0,0.04); }
.panel + .panel { margin-top: 1rem; }
.panel-title { font-size: 0.95rem; font-weight: 700; margin-bottom: .5rem; }
.panel-list { display: grid; gap: .25rem; }
.panel-item { display: flex; gap: .5rem; align-items: center; padding: .5rem .5rem; border-radius: 8px; color: var(--text); text-decoration: none; border: 1px solid transparent; }
.panel-item:hover { background: #f3f4f6; border-color: var(--border); }
.panel-icon { font-size: 1rem; }

.content { display: grid; gap: 1.25rem; }
.welcome { padding: .25rem 0 .5rem; }
.welcome-title { font-size: 1.5rem; font-weight: 700; }
.welcome-subtitle { color: var(--muted); font-size: .95rem; }

.service-section { background: transparent; border: none; border-radius: 0; padding-bottom: 1.5rem; }
/* faint separators between sections */
.service-section + .service-section { border-top: 1px solid #E5E5E5; padding-top: 1rem; }
.section-head { display:flex; align-items: baseline; justify-content: space-between; margin-bottom: .75rem; position: relative; }
.section-head h2 { font-size: 1.1rem; font-weight: 400; }
/* Recommended heading (Figma) */
#recommended-heading { font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'Liberation Sans', sans-serif; font-weight: 400; font-size: 24px; line-height: 32px; color: #171717; }
.section-subtitle { color: var(--muted); font-size: .85rem; margin-top: .25rem; }
.section-note { color: var(--muted); font-size: .75rem; }
.category-section .section-head { padding-left: 20px; display: block; min-height: 48px; }
.category-section .section-head::before { content: ""; position: absolute; left: 0; top: 0; height: 48px; width: 6px; background: linear-gradient(180deg, #FF7A00 0%, #EF7D00 100%); border-radius: 3px; }
.category-section .section-head h2 { font-size: 20px; line-height: 28px; font-weight: 400; color: #171717; margin: 0; }
.category-section .section-subtitle { font-size: 14px; line-height: 20px; color: #525252; margin: 0; }

/* Cards reuse existing ServiceCard DOM, override visuals */
.card-grid { display: grid; grid-template-columns: repeat(4, minmax(0,1fr)); gap: .75rem; }
.card-row { display: grid; grid-template-columns: repeat(3, minmax(0,1fr)); gap: .75rem; }

.service-card { background: var(--surface); border: 1px solid var(--border); border-radius: 8px; padding: .75rem; min-height: auto; height: auto; box-shadow: 0 1px 2px rgba(0,0,0,.04); }
.service-card:hover { transform: translateY(-2px); background: #fff; border-color: #d1d5db; box-shadow: 0 6px 16px rgba(0,0,0,0.08); }
.service-image { display: none; }
.service-title { color: var(--text); font-weight: 700; font-size: .95rem; }
.service-description { color: var(--muted); font-size: .8rem; }
.service-footer span { color: #fff; }
.service-footer { margin-top: .5rem; border-top: none; padding-top: 0; }
.service-footer .external-link { color: #fff; }

.service-card .service-footer span { background: var(--cta); color: #fff; padding: .4rem .6rem; border-radius: 8px; display: inline-flex; align-items:center; gap: .25rem; }
.service-card .external-link { display: none; }

.service-card .badge { display: inline-block; font-size: .68rem; font-weight: 700; color: var(--highlight); background: #fff7ed; border: 1px solid #fed7aa; padding: .15rem .4rem; border-radius: 999px; margin-left: .25rem; }

.footer-full {
  background: #FAFAFA;
  width: 100vw;
  margin-left: calc(50% - 50vw);
  margin-right: calc(50% - 50vw);
  margin-top: 8%;
  padding-top: 32px;
  padding-bottom: 40px;
  border-radius: 0;
  border-top: 1px solid #E5E5E5;

}
.footer-inner { max-width: 1280px; margin: 0 auto; padding: 0 80px; position: relative; }
.footer-grid { max-width: 1232px; display: grid; grid-template-columns: repeat(4, 284px); gap: 48px; }
.footer-col { position: relative; width: 284px; }
.footer-title { font-size: 16px; line-height: 24px; color: #171717; font-weight: 400; margin: 0 0 8px; }
.footer-sub { font-size: 14px; line-height: 20px; color: #525252; margin: 8px 0 16px; max-width: 274px; }

.footer-website-card { display: block; width: 284px; min-height: 106px; background: #FFFFFF; border: 1px solid #E5E5E5; border-radius: 8px; padding: 17px; color: inherit; text-decoration: none; }
.fwc-row { display: flex; align-items: center; gap: 8px; margin-bottom: 10px; }
.fwc-icon { width: 16px; height: 16px; }
.fwc-title { font-size: 14px; line-height: 20px; color: #171717; }
.fwc-desc { margin: 8px 0 8px; font-size: 12px; line-height: 15px; color: #525252; }
.fwc-link { font-size: 12px; line-height: 15px; color: #171717; }

.footer-list { list-style: none; padding: 0; margin: 8px 0 0; }
.footer-list li { font-size: 14px; line-height: 20px; color: #525252; margin: 8px 0; }

.footer-legal { max-width: 1232px; margin-top: 26px; padding-top: 16px; border-top: 1px solid #E5E5E5; text-align: center; font-size: 14px; line-height: 20px; color: #737373; }

/* Responsive */
@media (max-width: 1100px){ .card-grid { grid-template-columns: repeat(3,1fr);} }
@media (max-width: 900px){ .layout { grid-template-columns: 1fr; } .sidebar { position: static; order: 2; } .content { order: 1; } .card-row { grid-template-columns: repeat(2,1fr);} .card-grid { grid-template-columns: repeat(2,1fr);} }
@media (max-width: 560px){ .card-row, .card-grid { grid-template-columns: 1fr; } .topbar-inner { padding: .5rem .75rem; } .search-input{ width: 180px; } }
